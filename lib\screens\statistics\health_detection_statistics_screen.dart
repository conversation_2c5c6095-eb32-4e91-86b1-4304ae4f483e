import 'dart:async';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../constants/health_detection.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 健康监测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() =>
      _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState
    extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.multiLine;
  DateTime _currentDate = DateTime.now();
  Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  /// 散点图游标位置（X轴坐标，范围0-47，对应24小时的30分钟间隔）
  double _cursorPosition = 0.0;

  /// 是否显示游标和提示信息
  bool _showCursor = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false)
            .device
            ?.deviceName ??
        '';
    ChartService chartService =
        Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchHealthDetectionChartData(
              device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchHealthDetectionChartData(
              device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchHealthDetectionChartData(
              device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康监测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动切换图表类型
                final availableTypes =
                    ChartTypePresets.getHealthDetectionStatistics(period);
                if (availableTypes.isNotEmpty &&
                    !availableTypes.contains(_selectedChartType)) {
                  _selectedChartType = availableTypes.first;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '健康监测趋势',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          SizedBox(height: 16),
                          Expanded(
                            child: _buildChart(),
                          ),
                        ],
                      ),
                    ),
                  ),
          ),

          // 统计信息
          //if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedPeriod) {
      case TimePeriod.day:
        return _buildScatterChart(); // 日模式使用散点图
      case TimePeriod.week:
      case TimePeriod.month:
        return _buildHorizontalBarChart(); // 周模式和月模式使用水平条形图
      default:
        return _buildScatterChart();
    }
  }

  /// 聚合健康监测数据按时间分组（用于散点图）
  Map<HealthDetection, List<FlSpot>> _aggregateHealthDetectionDataForScatter() {
    final Map<HealthDetection, List<FlSpot>> result = {};
    // 初始化所有监测项目的数据列表
    for (final detection in HealthDetection.values) {
      result[detection] = [];
      double x = 0;
      for (final data in _healthData[detection.displayName]) {
        // 只有当数据不为null且不为0时才添加散点
        if (data != null && data != 0) {
          result[detection]!.add(FlSpot(x, detection.severityLevel.toDouble()));
        }
        x++;
      }
    }
    return result;
  }

  /// 构建散点图（健康监测趋势）
  Widget _buildScatterChart() {
    final aggregatedData = _aggregateHealthDetectionDataForScatter();

    // 使用所有监测项目数据
    final visibleData = aggregatedData;

    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return Expanded(
      child: Column(
        children: [
          // 散点图区域
          Expanded(
            child: AspectRatio(
              aspectRatio: 2.0, // 设置宽高比为2:1
              child: Stack(
                children: [
                  // 主散点图
                  GestureDetector(
                    onTapDown: (details) {
                      _handleDirectTouch(details.localPosition);
                    },
                    onTapUp: (details) {
                      _hideCursor(); // 手指离开屏幕时隐藏游标
                    },
                    onPanStart: (details) {
                      _handleDirectTouch(details.localPosition);
                    },
                    onPanUpdate: (details) {
                      _handleDirectTouch(details.localPosition);
                    },
                    onPanEnd: (details) {
                      _hideCursor(); // 拖拽结束时隐藏游标
                    },
                    child: ScatterChart(
                      ScatterChartData(
                        gridData: FlGridData(
                          show: true,
                          horizontalInterval: 1.0, // 设置水平网格线间隔为1，对应严重等级1-10
                          getDrawingHorizontalLine: (value) {
                            // 只在严重等级1-10的位置显示网格线，并使用对应健康监测项目的颜色
                            if (value >= 1 && value <= 10 && value % 1 == 0) {
                              final severityLevel = value.toInt();
                              final healthDetection =
                                  HealthDetection.values.firstWhere(
                                (detection) =>
                                    detection.severityLevel == severityLevel,
                                orElse: () => HealthDetection.fever,
                              );
                              final color = Color(int.parse(
                                  '0xFF${healthDetection.colorHex.substring(1)}'));

                              return FlLine(
                                color: color, // 使用健康监测项目颜色，最高透明度
                                strokeWidth: 1,
                                dashArray: [5, 5], // 设置虚线样式
                              );
                            }
                            return FlLine(
                                color: Colors.transparent, strokeWidth: 0);
                          },
                          getDrawingVerticalLine: (value) {
                            return FlLine(
                              color: Colors.grey.shade300,
                              strokeWidth: 1,
                              dashArray: [5, 5],
                            );
                          },
                        ),
                        titlesData: FlTitlesData(
                          leftTitles: AxisTitles(
                              sideTitles:
                                  SideTitles(showTitles: false)), // 隐藏左侧标题
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                              interval: 12.0, // 每12个点显示一次标签（6小时间隔）
                              getTitlesWidget: (value, meta) {
                                return Padding(
                                  padding: const EdgeInsets.only(
                                      top: 8.0), // 向下推进横坐标值
                                  child: Text(
                                    _getBottomTitleForScatter(value.toInt()),
                                    style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.grey.shade600),
                                  ),
                                );
                              },
                            ),
                          ),
                          rightTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              reservedSize: 100, // 增加预留空间，确保长标签不被遮挡
                              interval: 1.0, // 每个严重等级显示一个标签
                              getTitlesWidget: (value, meta) {
                                final severityLevel = value.toInt();
                                if (severityLevel >= 1 && severityLevel <= 10) {
                                  final healthDetection =
                                      HealthDetection.values.firstWhere(
                                    (detection) =>
                                        detection.severityLevel ==
                                        severityLevel,
                                    orElse: () => HealthDetection.fever,
                                  );
                                  return Container(
                                    width: 95, // 固定宽度确保文字不溢出
                                    padding: const EdgeInsets.only(left: 4.0),
                                    child: Text(
                                      healthDetection.displayName,
                                      style: TextStyle(
                                        fontSize: 9, // 稍微减小字体以适应空间
                                        color: Colors.grey.shade700,
                                      ),
                                      textAlign: TextAlign.left,
                                      overflow: TextOverflow.ellipsis, // 防止文字溢出
                                      maxLines: 1,
                                    ),
                                  );
                                }
                                return SizedBox.shrink();
                              },
                            ),
                          ),
                          topTitles: AxisTitles(
                              sideTitles: SideTitles(showTitles: false)),
                        ),
                        borderData: FlBorderData(show: false), // 去掉坐标轴外框
                        scatterTouchData: ScatterTouchData(
                          enabled: false, // 禁用fl_chart的触摸，使用自定义手势
                        ),
                        scatterSpots: _buildScatterSpots(visibleData),
                        minX: 0,
                        maxX: 47, // 24小时 * 2（30分钟间隔）- 1
                        minY: 0,
                        maxY: _getMaxYValueForScatter(visibleData),
                      ),
                    ),
                  ),
                  // 游标线图层（使用LineChart绘制垂直线）
                  if (_showCursor) _buildCursorLineChart(visibleData),
                  // 悬浮提示层（浮动在图表上方）
                  if (_showCursor) _buildFloatingTooltip(aggregatedData),
                ],
              ),
            ),
          ),
          // 底部图例
          _buildBottomLegend(),
        ],
      ),
    );
  }

  /// 构建底部图例
  Widget _buildBottomLegend() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Wrap(
        spacing: 12.0,
        runSpacing: 8.0,
        alignment: WrapAlignment.center,
        children: HealthDetection.values.map((detection) {
          final color =
              Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 4),
              Text(
                detection.displayName,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 构建水平条形图（各类健康监测的出现次数）
  Widget _buildHorizontalBarChart() {
    // 统计各类健康监测的出现次数
    final detectionCounts = <HealthDetection, int>{};

    for (final status in HealthDetection.values) {
      detectionCounts[status] = _healthData[status.displayName]!
          .fold(0, (prev, element) => prev + element);
    }

    // 确保所有10种监测项目都有数据（没有的设为0）
    for (final detection in HealthDetection.values) {
      detectionCounts.putIfAbsent(detection, () => 0);
    }

    if (detectionCounts.values.every((count) => count == 0)) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    // 按照固定顺序排列（从上到下）：发热、呼吸异常、运动不稳、步态不对称、步态规律性下降、夜醒、睡眠不足、运动复杂度异常、活动模式改变、kcal下降
    final sortedEntries = HealthDetection.values.map((detection) {
      return MapEntry(detection, detectionCounts[detection]!);
    }).toList();

    // 反转顺序，使发热在顶部
    // final reversedEntries = sortedEntries.reversed.toList();
    final reversedEntries = sortedEntries;

    // 计算最大值用于比例计算
    final maxCount =
        reversedEntries.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8), // 减少padding
        child: Column(
          children: reversedEntries.map((entry) {
            final detection = entry.key;
            final count = entry.value;
            final color =
                Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
            final percentage = maxCount > 0 ? count / maxCount : 0.0;

            return Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 0.5), // 进一步减少间距
                child: Row(
                  children: [
                    // 条形图区域
                    Expanded(
                      child: Stack(
                        children: [
                          // 背景条
                          Container(
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          // 数据条（彩色条纹，不包含标签）
                          FractionallySizedBox(
                            widthFactor: percentage,
                            child: Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          // 标签（在浅灰色背景的最左侧，最顶层显示）
                          Positioned(
                            left: 8,
                            top: 0,
                            bottom: 0,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                detection.displayName, // 在浅灰色背景最左侧的标签
                                style: TextStyle(
                                  color: Colors.white, // 白色文字，在最顶层显示
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 8),
                    // 外部数值显示（右侧）
                    SizedBox(
                      width: 30,
                      child: Text(
                        '$count',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 获取散点图底部标题（30分钟间隔）
  String _getBottomTitleForScatter(int index) {
    final hour = index ~/ 2;
    final minute = (index % 2) * 30;
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// 获取散点图Y轴最大值
  double _getMaxYValueForScatter(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 获取散点图Y轴间隔
  double _getYAxisIntervalForScatter(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 1.0;

    final maxValue = _getMaxYValueForScatter(visibleData);

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建散点图的散点数据
  List<ScatterSpot> _buildScatterSpots(
      Map<HealthDetection, List<FlSpot>> visibleData) {
    final List<ScatterSpot> scatterSpots = [];

    for (final entry in visibleData.entries) {
      final detection = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));

      for (final spot in spots) {
        scatterSpots.add(
          ScatterSpot(
            spot.x,
            spot.y,
            dotPainter: FlDotCirclePainter(
              radius: 2.0,
              color: color,
            ),
          ),
        );
      }
    }

    return scatterSpots;
  }

  /// 处理直接触摸事件
  void _handleDirectTouch(Offset localPosition) {
    // 计算图表区域的边距（与fl_chart内部布局匹配）
    const double leftMargin = 16.0; // 左边距
    const double rightMargin = 41.0; // 右边距（25 + 16）
    const double topMargin = 16.0; // 上边距
    const double bottomMargin = 56.0; // 下边距（40 + 16）

    // 获取当前widget的尺寸
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final size = renderBox.size;
    final chartWidth = size.width - leftMargin - rightMargin;
    final chartHeight = size.height - topMargin - bottomMargin;

    // 检查触摸点是否在图表区域内
    final relativeX = localPosition.dx - leftMargin;
    final relativeY = localPosition.dy - topMargin;

    if (relativeX < 0 ||
        relativeX > chartWidth ||
        relativeY < 0 ||
        relativeY > chartHeight) {
      return; // 触摸点在图表区域外
    }

    // 将像素坐标转换为数据坐标（实时跟随手指）
    final dataX = (relativeX / chartWidth) * 47;

    // 不进行吸附，让游标实时跟随手指位置
    final clampedX = dataX.clamp(0.0, 47.0);

    setState(() {
      _cursorPosition = clampedX;
      _showCursor = true;
    });
  }

  /// 隐藏游标
  void _hideCursor() {
    setState(() {
      _showCursor = false;
    });
  }

  /// 构建游标线图表（使用LineChart绘制垂直线）
  Widget _buildCursorLineChart(Map<HealthDetection, List<FlSpot>> visibleData) {
    final maxY = _getMaxYValueForScatter(visibleData);

    return IgnorePointer(
      child: LineChart(
        LineChartData(
          gridData: FlGridData(show: false), // 不显示网格
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: false,
                reservedSize: 40, // 与主图表保持一致的底部预留空间
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: false,
                reservedSize: 25, // 与主图表保持一致的右侧预留空间
              ),
            ),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 不显示边框
          lineBarsData: [
            LineChartBarData(
              spots: [
                FlSpot(_cursorPosition, maxY * 0.05), // 从Y轴5%位置开始，避免与顶部重叠
                FlSpot(_cursorPosition, maxY * 0.85), // 到Y轴85%位置结束，避免延伸到X轴标签区域
              ],
              isCurved: false,
              color: Colors.blue.withOpacity(0.8),
              barWidth: 3,
              isStrokeCapRound: false,
              dotData: FlDotData(show: false), // 不显示点
              belowBarData: BarAreaData(show: false), // 不显示填充区域
            ),
          ],
          minX: 0,
          maxX: 47,
          minY: 0,
          maxY: maxY,
          lineTouchData: LineTouchData(enabled: false), // 禁用触摸
        ),
      ),
    );
  }

  /// 构建浮动提示
  Widget _buildFloatingTooltip(
      Map<HealthDetection, List<FlSpot>> aggregatedData) {
    // 将游标位置吸附到最近的时间点用于数据显示
    final snappedPosition = _cursorPosition.round().toDouble().clamp(0.0, 47.0);
    final timeLabel = _getBottomTitleForScatter(snappedPosition.toInt());

    // 获取该时间点的所有健康异常项目
    final detectedItems = <String>[];

    for (final entry in aggregatedData.entries) {
      for (final spot in entry.value) {
        if (spot.x.toInt() == snappedPosition.toInt()) {
          // 根据Y值确定显示内容
          final severityLevel = spot.y.toInt();
          String itemText;

          if (severityLevel == 0) {
            itemText = '无数据';
          } else {
            final healthDetection = HealthDetection.values.firstWhere(
              (detection) => detection.severityLevel == severityLevel,
              orElse: () => HealthDetection.fever,
            );
            itemText = healthDetection.displayName;
          }

          if (!detectedItems.contains(itemText)) {
            detectedItems.add(itemText);
          }
        }
      }
    }

    return Positioned(
      top: 10, // 固定在图表上方
      left: 16,
      right: 16,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 时间标题
            Text(
              '时间: $timeLabel',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            SizedBox(height: 8),
            // 异常信息区域 - 移除高度限制，让内容完全显示
            if (detectedItems.isNotEmpty)
              Wrap(
                spacing: 6,
                runSpacing: 4,
                children: detectedItems.map((item) {
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Text(
                      item,
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.red.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              )
            else
              Text(
                '该时间点无异常检测',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
