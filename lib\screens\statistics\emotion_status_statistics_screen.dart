import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:provider/provider.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../constants/emotion_status.dart';
import '../../services/chart_service.dart';
import '../../providers/device_provider.dart';

/// 情绪状态统计界面
class EmotionStatusStatisticsScreen extends StatefulWidget {
  @override
  _EmotionStatusStatisticsScreenState createState() =>
      _EmotionStatusStatisticsScreenState();
}

class _EmotionStatusStatisticsScreenState
    extends State<EmotionStatusStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.line;
  DateTime _currentDate = DateTime.now();
  Map<String, dynamic> _healthData = {};
  bool _isLoading = true;

  /// 多曲线图中各情绪状态的可见性控制
  Map<EmotionStatus, bool> _visibleStatuses = {
    for (EmotionStatus status in EmotionStatus.values)
      status: EmotionStatus.values.indexOf(status) == 0 ? true : false,
  };

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // 获取设备名称和图表服务
    String device_name = Provider.of<DeviceProvider>(context, listen: false)
            .device
            ?.deviceName ??
        '';
    ChartService chartService =
        Provider.of<ChartService>(context, listen: false);

    try {
      Map<String, dynamic> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = await chartService.fetchEmotionStatusChartData(
              device_name, 'day', _currentDate);
          break;
        case TimePeriod.week:
          data = await chartService.fetchEmotionStatusChartData(
              device_name, 'week', _currentDate);
          break;
        case TimePeriod.month:
          data = await chartService.fetchEmotionStatusChartData(
              device_name, 'month', _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  /// 聚合情绪数据按日期分组
  Map<EmotionStatus, List<FlSpot>> _aggregateEmotionDataByDate() {
    final Map<EmotionStatus, List<FlSpot>> result = {};
    // 初始化所有状态的数据列表
    for (final status in EmotionStatus.values) {
      double x = 0;
      for (final data in _healthData[status.displayName]) {
        result[status] ??= [];
        result[status]!.add(FlSpot(x, data.toDouble()));
        x++;
      }
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('情绪状态统计'),
        backgroundColor: Colors.purple.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),

          // 图表区域
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : Card(
                  elevation: 4,
                  margin: EdgeInsets.all(16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getChartTitle(),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 20),
                        _buildChart(),
                      ],
                    ),
                  ),
                ),

          // 多曲线图图例
          if (!_isLoading && _selectedChartType == ChartViewType.multiLine)
            _buildEmotionStatusLegend(),

          // 统计信息
          // if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 获取图表标题
  String _getChartTitle() {
    switch (_selectedChartType) {
      case ChartViewType.line:
        return '情绪水平变化';
      case ChartViewType.groupedBar:
        return '情绪状态分布';
      case ChartViewType.multiLine:
        return '各情绪状态趋势';
      default:
        return '情绪状态统计';
    }
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedPeriod) {
      case TimePeriod.day:
        _selectedChartType = ChartViewType.line;
        return _buildDayModeScrollableChart();

      case TimePeriod.week:
        _selectedChartType = ChartViewType.groupedBar;
        return Column(
          children: [
            SizedBox(
              height: 400,
              child: _buildGroupedBarChart(),
            ),
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 8,
              runSpacing: 4,
              children: EmotionStatus.values.map((status) {
                final color =
                    Color(int.parse('0xFF${status.colorHex.substring(1)}'));
                return _buildLegendItem(status.displayName, color);
              }).toList(),
            ),
          ],
        );
      case TimePeriod.month:
        _selectedChartType = ChartViewType.multiLine;
        return SizedBox(height: 200, width: 400, child: _buildMultiLineChart());
    }
  }

  /// 构建日模式可滚动图表
  Widget _buildDayModeScrollableChart() {
    return Column(
      children: [
        SizedBox(
          height: 400,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: 1440, // 288个数据点 * 5像素宽度，确保所有数据点都能显示
              
              child: _buildLineChart(),
            ),
          ),
        ),
        SizedBox(height: 16), // 图表与图例之间的间距
        _buildEmotionColorLegend(), // 添加颜色图例
      ],
    );
  }

  /// 构建日模式曲线图（情绪水平变化）
  Widget _buildLineChart() {
    // 构建单条连续曲线的FlSpot列表
    List<FlSpot> spots = [];
    double k = 0;
    for (final edata in _healthData['emotion']!) {
      if (edata == "") {
        spots.add(FlSpot(k, 0));
      } else {
        final data = EmotionStatus.fromString(edata).emotionLevel;
        spots.add(FlSpot(k, data.toDouble()));
      }
      k++;
    }

    return LineChart(
      LineChartData(
        minY: 0,
        maxY: 7,
        gridData: FlGridData(
          show: true,
          horizontalInterval: 1.0, // 设置水平网格线间隔为1，对应情绪等级1-7
          getDrawingHorizontalLine: (value) {
            // 只在情绪等级1-7的位置显示网格线，并使用对应情绪状态的颜色
            if (value >= 1 && value <= 7 && value % 1 == 0) {
              final emotionLevel = value.toInt();
              final emotionStatus = EmotionStatus.values.firstWhere(
                (status) => status.emotionLevel == emotionLevel,
                orElse: () => EmotionStatus.stable,
              );
              final color = Color(
                  int.parse('0xFF${emotionStatus.colorHex.substring(1)}'));

              return FlLine(
                color: color, // 使用情绪状态颜色，最高透明度
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            }
            return FlLine(color: Colors.transparent, strokeWidth: 0);
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.transparent, // 设置为透明色，不显示竖线
              strokeWidth: 0,
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: false,
              interval: 1,
            ),
          ), // 隐藏左侧标题
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 100, // 增加预留空间，确保长标签不被遮挡
              interval: 1.0, // 每个情绪等级显示一个标签
              getTitlesWidget: (value, meta) {
                final emotionLevel = value.toInt();
                if (emotionLevel >= 1 && emotionLevel <= 7) {
                  final emotionStatus = EmotionStatus.values.firstWhere(
                    (status) => status.emotionLevel == emotionLevel,
                    orElse: () => EmotionStatus.stable,
                  );
                  return Container(
                    width: 95, // 固定宽度确保文字不溢出
                    padding: const EdgeInsets.only(left: 4.0),
                    child: Text(
                      emotionStatus.displayName,
                      style: TextStyle(
                        fontSize: 9, // 稍微减小字体以适应空间
                        color: Colors.grey.shade700,
                      ),
                      textAlign: TextAlign.left,
                      overflow: TextOverflow.ellipsis, // 防止文字溢出
                      maxLines: 1,
                    ),
                  );
                }
                return SizedBox.shrink();
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 25, // 增加底部预留空间，拉开与绘图区域的距离
              interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
              getTitlesWidget: (value, meta) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                  child: Text(
                    _getBottomTitle(value.toInt()),
                    style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                  ),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border(
            bottom: BorderSide(color: Colors.grey.shade400, width: 1),
          ),
        ), // 添加底部边框
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            fitInsideHorizontally: true,
            fitInsideVertically: true,
            tooltipHorizontalOffset: -12, // 向图表内部偏移，避免被右侧标签遮挡
            tooltipMargin: 10,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final index = barSpot.x.toInt();
                String timeRange = '';

                // 日模式显示时间段（5分钟间隔）
                final hour = index ~/ 12;
                final minute = (index % 12) * 5;
                final nextMinute = minute + 5;
                if (nextMinute == 60) {
                  timeRange =
                      '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
                } else {
                  timeRange =
                      '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
                }

                // 根据Y值确定情绪状态
                final emotionLevel = barSpot.y.toInt();
                final emotionStatus = EmotionStatus.values.firstWhere(
                  (status) => status.emotionLevel == emotionLevel,
                  orElse: () => EmotionStatus.stable,
                );

                return LineTooltipItem(
                  '$timeRange\n${emotionStatus.displayName}',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }).toList();
            },
          ),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: false,
            color: Colors.transparent,
            barWidth: 2,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                // 根据Y值确定对应的情绪状态和颜色
                final emotionLevel = spot.y.toInt();

                // 如果Y值为0，表示无数据，不显示数据点
                if (emotionLevel == 0) {
                  return FlDotCirclePainter(
                    radius: 0, // 半径为0，不显示点
                    color: Colors.transparent,
                  );
                }

                final emotionStatus = EmotionStatus.values.firstWhere(
                  (status) => status.emotionLevel == emotionLevel,
                  orElse: () => EmotionStatus.stable,
                );
                final statusColor = Color(
                    int.parse('0xFF${emotionStatus.colorHex.substring(1)}'));

                return FlDotCirclePainter(
                  radius: 1,
                  color: statusColor.withOpacity(0.4), // 使用情绪状态颜色，降低饱和度
                  strokeWidth: 1.5, // 设置边框宽度
                  strokeColor: statusColor.withOpacity(0.8), // 边框使用相同颜色但更深
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 日模式获取底部标题间隔
  double _getBottomTitleInterval() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return 36.0; // 日模式：3小时间隔(36个5分钟)
      case TimePeriod.week:
        return 1.0; // 周模式：每天显示
      case TimePeriod.month:
        return 5.0; // 月模式：每5天显示一个标签
    }
  }

  /// 日模式获取底部标题
  String _getBottomTitle(int index) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${(index ~/ 12)}:${((index % 12) * 5).toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${(index + 1)}';
      case TimePeriod.month:
        return '${(index + 1)}日';
    }
  }

  /// 构建周模式百分比堆叠柱状图（7种情绪状态分布）
  Widget _buildGroupedBarChart() {
    // 构建BarChartGroupData列表
    final barGroups = <BarChartGroupData>[];

    for (int i = 0; i < 7; i++) {
      // 计算当天所有情绪状态的总数
      int totalCount = 0;
      final dayData = <EmotionStatus, int>{};

      for (final status in EmotionStatus.values) {
        // 数据补全逻辑：检查数组长度，避免越界异常
        final statusData = _healthData[status.displayName] as List?;
        final count = (statusData != null && i < statusData.length)
            ? statusData[i] as int
            : 0; // 如果数据不足7天，用0补全缺失的天数
        dayData[status] = count;
        totalCount += count;
      }

      // 如果当天没有数据，显示空柱子
      if (totalCount == 0) {
        barGroups.add(
          BarChartGroupData(
            x: i,
            barRods: [
              BarChartRodData(
                fromY: 0,
                toY: 100,
                color: Colors.grey.shade300,
                width: 30,
                borderRadius: BorderRadius.circular(0),
              ),
            ],
          ),
        );
        continue;
      }

      // 构建单个堆叠柱状图的数据段
      final stackedSegments = <BarChartRodStackItem>[];
      double currentY = 0;

      for (final status in EmotionStatus.values) {
        final count = dayData[status]!;
        final percentage = (count / totalCount) * 100;

        if (percentage > 0) {
          final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));
          stackedSegments.add(
            BarChartRodStackItem(
              currentY,
              currentY + percentage,
              color,
            ),
          );
          currentY += percentage;
        }
      }

      // 创建单个堆叠柱状图
      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              fromY: 0,
              toY: 100,
              color: Colors.transparent, // 背景透明
              width: 30,
              borderRadius: BorderRadius.circular(0),
              rodStackItems: stackedSegments,
            ),
          ],
        ),
      );
    }

    return BarChart(
      BarChartData(
        gridData: FlGridData(
          show: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.transparent, // 设置为透明色，不显示竖线
              strokeWidth: 0,
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 35, // 增加空间以显示百分比符号
              interval: 25, // 设置Y轴间隔为25%
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toInt()}%',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  textAlign: TextAlign.right,
                );
              },
            ),
          ), // 显示右侧百分比标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
              getTitlesWidget: (value, meta) {
                return Text(
                  _getGroupTitle(value.toInt()),
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        maxY: 100, // 设置Y轴最大值为100%
        minY: 0, // 设置Y轴最小值为0%
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final groupTitle = _getGroupTitle(groupIndex);

              // 计算当天所有情绪状态的总数和当前状态的数据
              int totalCount = 0;
              final dayData = <EmotionStatus, int>{};

              for (final status in EmotionStatus.values) {
                // 数据补全逻辑：检查数组长度，避免越界异常
                final statusData = _healthData[status.displayName] as List?;
                final count =
                    (statusData != null && groupIndex < statusData.length)
                        ? statusData[groupIndex] as int
                        : 0; // 如果数据不足7天，用0补全缺失的天数
                dayData[status] = count;
                totalCount += count;
              }

              if (totalCount == 0) {
                return BarTooltipItem(
                  '$groupTitle\n暂无数据',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }

              // 显示当天所有情绪状态的详细信息
              final tooltipLines = <String>[groupTitle];

              for (final status in EmotionStatus.values) {
                final count = dayData[status]!;
                if (count > 0) {
                  final percentage = (count / totalCount) * 100;
                  tooltipLines.add(
                      '${status.displayName}: ${percentage.toStringAsFixed(1)}% (${count}次)');
                }
              }

              return BarTooltipItem(
                tooltipLines.join('\n'),
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
        barGroups: barGroups,
      ),
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// 构建情绪状态颜色图例（日模式专用）
  Widget _buildEmotionColorLegend() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 12, // 水平间距
        runSpacing: 8, // 垂直间距
        children: EmotionStatus.values.map((status) {
          final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle, // 使用圆形颜色块
                ),
              ),
              SizedBox(width: 4),
              Text(
                status.displayName,
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 获取分组索引
  int _getGroupIndex(int dataIndex) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return dataIndex ~/ 12; // 每12个数据点为一组（1小时，5分钟间隔）
      case TimePeriod.week:
        return dataIndex ~/ 15; // 每15个数据点为一组（每天一组，因为每天有15个数据点）
      case TimePeriod.month:
        return dataIndex ~/ 7; // 每周一组
    }
  }

  /// 获取分组标题
  String _getGroupTitle(int groupIndex) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        final hour = groupIndex * 1; // 每小时
        return '${hour}时';
      case TimePeriod.week:
        final date = _currentDate.subtract(Duration(days: 6 - groupIndex));
        return '${date.month}/${date.day}';
      case TimePeriod.month:
        final weekNumber = groupIndex + 1;
        return '第${weekNumber}周';
    }
  }

  /// 获取柱状图Y轴间隔
  double _getBarChartYAxisInterval(double maxValue) {
    if (maxValue <= 0) return 1.0;

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建多曲线图（月模式：7条情绪状态曲线）
  Widget _buildMultiLineChart() {
    final aggregatedData = _aggregateEmotionDataByDate();

    // 过滤出可见的状态数据
    final visibleData = <EmotionStatus, List<FlSpot>>{};
    for (final entry in aggregatedData.entries) {
      if (_visibleStatuses[entry.key] == true && entry.value.isNotEmpty) {
        visibleData[entry.key] = entry.value;
      }
    }
    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
              dashArray: [5, 5], // 设置虚线样式
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles:
              AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 12, // 减少预留空间，旋转后减小左侧空白
              interval: 6.0, // 每6天显示一个标签
              getTitlesWidget: (value, meta) {
                final day = value.toInt() + 1;
                return Text(
                  '${day}日',
                  style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 25, // 进一步减少右侧预留空间，让绘图区域更靠近右边缘
              interval: _getYAxisInterval(visibleData), // 设置Y轴间隔
              getTitlesWidget: (value, meta) {
                final interval = _getYAxisInterval(visibleData).toInt();
                final intValue = value.toInt();

                // 只显示符合间隔的非负整数值
                if (intValue >= 0 && interval > 0 && intValue % interval == 0) {
                  return Text(
                    intValue.toString(),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    textAlign: TextAlign.right,
                  );
                }
                return SizedBox.shrink(); // 不显示非间隔值
              },
            ),
          ), // 显示右侧数值标题
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        lineTouchData: LineTouchData(
          enabled: true,
          handleBuiltInTouches: true, // 确保内置触摸处理正常
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return _buildMultiLineTooltipItems(touchedBarSpots);
            },
          ),
          touchCallback:
              (FlTouchEvent event, LineTouchResponse? touchResponse) {
            // 不执行任何状态更新，只处理tooltip显示
            // 这样可以避免触摸时意外的状态改变
          },
        ),
        lineBarsData: _buildMultiLineChartBars(visibleData),
      ),
    );
  }

  /// 获取Y轴最大值（用于多曲线图）
  double _getMaxYValue(Map<EmotionStatus, List<FlSpot>> visibleData) {
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 获取Y轴间隔（用于多曲线图）
  double _getYAxisInterval(Map<EmotionStatus, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 1.0;

    final maxValue = _getMaxYValue(visibleData);

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }

  /// 构建多曲线图的曲线数据
  List<LineChartBarData> _buildMultiLineChartBars(
      Map<EmotionStatus, List<FlSpot>> visibleData) {
    final List<LineChartBarData> lineBars = [];

    for (final entry in visibleData.entries) {
      final status = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

      lineBars.add(
        LineChartBarData(
          spots: spots,
          isCurved: true, // 使用光滑曲线
          curveSmoothness: 0.15, // 设置曲线平滑度，值越大越平滑
          color: color,
          barWidth: 1.5, // 线条宽度
          dotData: FlDotData(show: false), // 不显示曲线上的小圆点
          belowBarData: BarAreaData(show: false), // 多曲线图不显示填充区域
        ),
      );
    }

    return lineBars;
  }

  /// 构建多曲线图的Tooltip项
  List<LineTooltipItem> _buildMultiLineTooltipItems(
      List<LineBarSpot> touchedBarSpots) {
    final List<LineTooltipItem> tooltipItems = [];

    if (touchedBarSpots.isNotEmpty) {
      final index = touchedBarSpots.first.x.toInt();
      final day = index + 1;
      final month = DateTime.now().month; // 使用当前月份
      final timeRange = '${month}月${day}日';

      // 为每个触摸点创建对应的tooltip项
      final visibleStatuses = EmotionStatus.values
          .where((status) => _visibleStatuses[status] == true)
          .toList();

      for (int i = 0; i < touchedBarSpots.length; i++) {
        final barSpot = touchedBarSpots[i];

        if (i < visibleStatuses.length) {
          final status = visibleStatuses[i];
          final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

          // 第一个tooltip项包含时间范围，其他项只显示状态信息
          final displayText = i == 0
              ? '$timeRange\n${status.displayName}: ${barSpot.y.toInt()}次'
              : '${status.displayName}: ${barSpot.y.toInt()}次';

          tooltipItems.add(
            LineTooltipItem(
              displayText,
              TextStyle(
                color: i == 0 ? Colors.white : color,
                fontWeight: i == 0 ? FontWeight.bold : FontWeight.w600,
                fontSize: i == 0 ? 12 : 11,
              ),
            ),
          );
        } else {
          // 如果没有对应的状态，添加空的tooltip项以保持数量一致
          tooltipItems.add(
            LineTooltipItem(
              '',
              const TextStyle(color: Colors.transparent),
            ),
          );
        }
      }
    }

    return tooltipItems;
  }

  /// 构建情绪状态图例（多曲线图用）
  Widget _buildEmotionStatusLegend() {
    return Card(
      elevation: 4,
      margin: EdgeInsets.all(16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '情绪状态图例',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            SizedBox(height: 8, width: 500),
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: EmotionStatus.values.map((status) {
                final color =
                    Color(int.parse('0xFF${status.colorHex.substring(1)}'));
                final isVisible = _visibleStatuses[status] ?? true;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _visibleStatuses[status] = !isVisible;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isVisible
                          ? color.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isVisible ? color : Colors.grey,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: isVisible ? color : Colors.grey,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          status.displayName,
                          style: TextStyle(
                            fontSize: 11,
                            color: isVisible
                                ? Colors.grey.shade700
                                : Colors.grey.shade500,
                            fontWeight:
                                isVisible ? FontWeight.w500 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    final emotionLevels = _healthData['level']!.map((d) => d).toList();
    final avgLevel = emotionLevels.fold(0, (sum, level) => sum + level) /
        emotionLevels.length;

    final mostCommonEmotion = _getMostCommonEmotion();
    final emotionVariety = _getEmotionVariety();

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                      '平均情绪', avgLevel.toStringAsFixed(1), Colors.purple),
                  _buildStatItem(
                      '最常见',
                      mostCommonEmotion.displayName,
                      Color(int.parse(
                          '0xFF${mostCommonEmotion.colorHex.substring(1)}'))),
                  _buildStatItem('情绪多样性', emotionVariety, Colors.orange),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('情绪稳定性', _getEmotionStability(), Colors.blue),
                  _buildStatItem(
                      '记录数', '${_healthData['level']!.length}', Colors.grey),
                  _buildStatItem('时间跨度', _getTimeSpan(), Colors.teal),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的情绪状态
  EmotionStatus _getMostCommonEmotion() {
    final emotionCounts = <EmotionStatus, int>{};
    for (final data in _healthData['level']!) {
      emotionCounts[EmotionStatus.values[data]] =
          (emotionCounts[EmotionStatus.values[data]] ?? 0) + 1;
    }
    return emotionCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// 获取情绪稳定性描述
  String _getEmotionStability() {
    final levels = _healthData['level']!.map((d) => d).toList() as List<int>;
    if (levels.isEmpty) return '无数据';

    final variance = _calculateVariance(levels);
    if (variance < 1.0) return '很稳定';
    if (variance < 2.0) return '较稳定';
    if (variance < 3.0) return '一般';
    return '波动大';
  }

  /// 计算方差
  double _calculateVariance(List<int> values) {
    final mean =
        values.fold<int>(0, (sum, value) => sum + value) / values.length;
    final squaredDiffs = values.map((value) => (value - mean) * (value - mean));
    return squaredDiffs.fold<double>(0, (sum, diff) => sum + diff) /
        values.length;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 获取情绪多样性描述
  String _getEmotionVariety() {
    final uniqueEmotions = _healthData['level']!.map((d) => d).toSet();
    final varietyCount = uniqueEmotions.length;

    if (varietyCount <= 2) return '单一';
    if (varietyCount <= 4) return '较少';
    if (varietyCount <= 6) return '丰富';
    return '很丰富';
  }

  /// 获取时间跨度描述
  String _getTimeSpan() {
    if (_healthData['level']!.isEmpty) return '无数据';

    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '24小时';
      case TimePeriod.week:
        return '7天';
      case TimePeriod.month:
        final daysInMonth =
            DateTime(_currentDate.year, _currentDate.month + 1, 0).day;
        return '${daysInMonth}天';
    }
  }
}
